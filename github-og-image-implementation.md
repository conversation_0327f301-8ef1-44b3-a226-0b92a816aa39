# GitHub分享卡片OG图片自动生成实现方案

## 🎯 项目概述

本文档详细记录了GitHub单用户分析页面和比较页面的OG图片自动生成功能的完整实现方案。该功能确保社交媒体分享时显示个性化的分享卡片，而不是默认头像。

## 📋 核心架构

### 整体设计思路
1. **可预测URL策略** - 使用固定格式的文件名，便于服务端预设
2. **隐藏渲染技术** - 在用户不可见的情况下渲染完整的分享卡片
3. **自动触发机制** - 分析完成后自动生成并上传OG图片
4. **图片处理优化** - 解决html2canvas的兼容性问题

### 技术栈
- **前端框架**: Nuxt 3 + Vue 3
- **截图库**: html2canvas-pro
- **存储**: AWS S3 + Cloudflare Worker
- **图片格式**: PNG (1200x630, 2x scale)

---

## 🔧 GitHub单用户分析页面实现

### 1. 可预测URL生成

```typescript
// utils/index.ts
export function getPredictableOgImageUrl(username: string): string {
  const fileName = `github-${username}-latest.png`
  return `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
}

export async function checkOgImageExists(username: string): Promise<boolean> {
  try {
    const url = getPredictableOgImageUrl(username)
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    return false
  }
}
```

### 2. S3上传基础设施

```typescript
// 获取预签名URL
export async function getPresignedUrl(fileType: 'image/png', fileName?: string) {
  const workerUrl = 'https://get-s3-url.pjharvey071.workers.dev'
  const response = await fetch(workerUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ fileType, fileName })
  })
  return response.json() // { uploadUrl, publicUrl }
}

// 直接上传到S3
export async function uploadFileToS3(blob: Blob, fileType: 'image/png', fileName?: string) {
  const { uploadUrl, publicUrl } = await getPresignedUrl(fileType, fileName)
  
  await fetch(uploadUrl, {
    method: 'PUT',
    headers: { 'Content-Type': fileType },
    body: blob
  })
  
  return publicUrl
}
```

### 3. 页面初始化SEO设置

```typescript
// pages/github/index.vue
if (username) {
  const predictableOgImageUrl = getPredictableOgImageUrl(username)
  
  useSeoMeta({
    title: `${username} - GitHub Developer Profile | DINQ`,
    description: `View ${username}'s GitHub developer profile...`,
    ogTitle: `${username} - GitHub Developer Profile`,
    ogDescription: `View ${username}'s GitHub developer profile...`,
    ogImage: predictableOgImageUrl, // 关键：预设可预测URL
    twitterCard: 'summary_large_image',
    twitterImage: predictableOgImageUrl,
  })
}
```

### 4. 隐藏渲染组件

```vue
<!-- 关键：正确的隐藏方式 -->
<div 
  v-if="githubData && !ogImageGenerated"
  class="hidden-render-container"
  :style="{
    position: 'fixed',
    top: '0',
    left: '0',
    transform: 'translateX(-100%)', // 核心：移出视窗但保持渲染
    zIndex: '-1',
    pointerEvents: 'none'
    // 不使用 opacity: 0 或 visibility: hidden
  }"
>
  <ShareCardGithub
    :show="true"
    :user="user"
    :all-props="..."
    @close="() => {}"
  />
</div>
```

### 5. 自动生成触发逻辑

```typescript
// 在分析结果返回后自动生成
const analyzeGitHubUser = async (username: string) => {
  const result = await callGitHubAnalyzeAPI(username)
  
  if (result.success) {
    githubData.value = result.data
    updateSeoMeta(result.data)
    
    // 自动生成OG图片
    nextTick(() => {
      generateOgImage(username)
    })
  }
}
```

### 6. 核心生成函数

```typescript
const generateOgImage = async (username: string) => {
  if (ogImageGenerated.value || !import.meta.client) return

  try {
    // 1. 等待DOM渲染完成
    await nextTick()
    
    // 2. 等待图片资源加载
    const images = shareCardElement.getElementsByTagName('img')
    await Promise.all([...images].map(img => {
      if (img.complete) return Promise.resolve()
      return new Promise(resolve => {
        img.onload = resolve
        img.onerror = resolve
      })
    }))
    
    // 3. 等待字体加载
    if (document.fonts) {
      await document.fonts.ready
    }
    
    // 4. 给浏览器额外渲染时间
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 5. 生成图片
    const canvas = await html2canvas(shareCardElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 1200,
      height: 630,
      onclone: handleImageProcessing // 关键：图片处理逻辑
    })
    
    // 6. 上传到S3
    const blob = await new Promise(resolve => 
      canvas.toBlob(resolve, 'image/png', 1.0)
    )
    const fileName = `github-${username}-latest.png`
    const publicUrl = await uploadFileToS3(blob, 'image/png', fileName)
    
    // 7. 更新meta标签
    updateSeoMetaWithOgImage(publicUrl)
    
  } catch (error) {
    console.error('OG图片生成失败:', error)
  }
}
```

### 7. 图片处理核心逻辑

```typescript
const handleImageProcessing = (clonedDoc, isDarkMode) => {
  const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github"]')
  
  // 1. 替换操作按钮为版权信息
  const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
  if (buttonContainer) {
    const copyrightDiv = clonedDoc.createElement('div')
    copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'
    buttonContainer.parentNode?.replaceChild(copyrightDiv, buttonContainer)
  }
  
  // 2. SVG图标替换为PNG
  const iconMappings = {
    '#icon-verified': '/image/sharecard/github-verify.png',
    '#icon-research': '/image/sharecard/overview.png',
    '#icon-add1': '/image/sharecard/additions.png',
    '#icon-trash-bin': '/image/sharecard/deletions.png',
    // ... 更多图标映射
  }
  
  const svgElements = clonedElement.querySelectorAll('svg.svg-icon')
  svgElements.forEach(svgEl => {
    const useElement = svgEl.querySelector('use')
    const iconId = useElement?.getAttribute('href')
    
    if (iconId && iconMappings[iconId]) {
      const imgElement = clonedDoc.createElement('img')
      imgElement.src = iconMappings[iconId]
      imgElement.className = svgEl.className
      svgEl.parentNode?.replaceChild(imgElement, svgEl)
    }
  })
  
  // 3. 修复背景图片
  const customBgCards = clonedElement.querySelectorAll('.custom-bg')
  customBgCards.forEach(cardEl => {
    const isAdditionsCard = cardEl.textContent?.includes('Additions')
    const isDeletionsCard = cardEl.textContent?.includes('Deletions')
    
    if (isDarkMode) {
      cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2xdark.png)', 'important')
    } else {
      if (isAdditionsCard) {
        cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x1.png)', 'important')
      } else if (isDeletionsCard) {
        cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x.png)', 'important')
      }
    }
  })
  
  // 4. 修复文字颜色
  // 5. 修复毛玻璃效果
  // 6. 修复高亮卡片
}
```

---

## 🔄 GitHub比较页面实现

### 1. 扩展工具函数

```typescript
// utils/index.ts
export function getPredictableCompareOgImageUrl(user1: string, user2: string): string {
  const fileName = `github-compare-${user1}-vs-${user2}-latest.png`
  return `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
}

export async function checkCompareOgImageExists(user1: string, user2: string): Promise<boolean> {
  try {
    const url = getPredictableCompareOgImageUrl(user1, user2)
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    return false
  }
}
```

### 2. API端点扩展

```typescript
// server/api/github/save-compare-og-image.post.ts
export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { user1, user2, ogImageUrl } = body
  
  // TODO: 保存到数据库
  console.log(`Saving compare OG image URL for ${user1} vs ${user2}: ${ogImageUrl}`)
  
  return { success: true }
})

// server/api/github/compare-og-image/[users].get.ts
export default defineEventHandler(async (event) => {
  const users = getRouterParam(event, 'users') // "user1-vs-user2"
  const [user1, user2] = users?.split('-vs-') || []
  
  // TODO: 从数据库获取
  return {
    success: true,
    user1, user2,
    ogImageUrl: null,
    hasOgImage: false
  }
})
```

### 3. 比较页面特有处理

#### 标签样式修复
```typescript
const fixCompareTagStyles = (clonedElement, isDarkMode) => {
  const allTags = clonedElement.querySelectorAll('.tag-component')
  
  allTags.forEach(tag => {
    // 通过父容器的justify类判断左右侧
    let isLeftSide = false  // justify-end (左侧用户)
    let isRightSide = false // justify-start (右侧用户)
    
    let parent = tag.parentElement
    while (parent && parent !== clonedElement) {
      if (parent.className.includes('justify-end')) {
        isLeftSide = true
        break
      }
      if (parent.className.includes('justify-start')) {
        isRightSide = true
        break
      }
      parent = parent.parentElement
    }
    
    // 应用对应的颜色方案
    if (isDarkMode) {
      if (isLeftSide) {
        // share-blue variant 深色模式
        tag.style.backgroundColor = '#3C4356'
        tag.style.borderColor = '#7F8EB7'
        tag.style.color = '#C2C5CE'
      } else if (isRightSide) {
        // share-orange variant 深色模式
        tag.style.backgroundColor = '#413834'
        tag.style.borderColor = '#71635E'
        tag.style.color = '#E1BCAD'
      }
    } else {
      // 亮色模式处理...
    }
  })
}
```

#### 雷达图Canvas处理
```typescript
const fixRadarChartCanvas = (clonedElement) => {
  const radarCharts = clonedElement.querySelectorAll('.radar-chart')
  
  radarCharts.forEach(radarChart => {
    const canvas = radarChart.querySelector('canvas')
    if (canvas) {
      try {
        // 将Chart.js Canvas转换为静态图片
        const dataURL = canvas.toDataURL('image/png')
        
        const img = document.createElement('img')
        img.src = dataURL
        img.style.width = canvas.style.width || `${canvas.width}px`
        img.style.height = canvas.style.height || `${canvas.height}px`
        
        // 替换canvas为img
        canvas.parentNode?.replaceChild(img, canvas)
        
      } catch (error) {
        // 如果转换失败，设置Canvas样式确保html2canvas能处理
        canvas.style.backgroundColor = 'transparent'
        canvas.style.display = 'block'
      }
    }
  })
}
```

---

## 📊 关键成功要素

### 1. 隐藏渲染的正确方式
- ✅ 使用 `transform: translateX(-100%)`
- ❌ 避免 `opacity: 0` 和 `visibility: hidden`

### 2. 资源加载等待
- 等待图片加载完成
- 等待字体加载完成
- 给浏览器额外渲染时间

### 3. 图片处理的完整性
- SVG → PNG 替换
- 背景图片修复
- 文字颜色修复
- CSS兼容性处理

### 4. 可预测的URL策略
- 固定文件名格式
- 服务端预设URL
- 解决时序问题

---

## 🎯 最终效果

### 单用户页面
- **URL格式**: `github-username-latest.png`
- **触发时机**: GitHub分析完成后
- **包含内容**: 用户信息、统计数据、编程语言、特色项目等

### 比较页面
- **URL格式**: `github-compare-user1-vs-user2-latest.png`
- **触发时机**: 比较分析完成后
- **包含内容**: 双用户对比、雷达图、标签、统计对比等

### 社交媒体兼容性
- ✅ Twitter Cards (summary_large_image)
- ✅ Facebook Open Graph
- ✅ LinkedIn分享
- ✅ 微信分享卡片
- ✅ 其他社交平台

---

## 🔧 代码复用率

- **复用的功能**: ~85%
  - S3上传逻辑
  - SVG图标替换
  - 样式修复函数
  - Meta标签更新
  - 资源加载等待

- **页面特有功能**: ~15%
  - 单用户页面：基础样式修复
  - 比较页面：标签样式、雷达图处理

这套方案可以直接应用到其他分享卡片组件，只需要调整图标映射、颜色方案和卡片识别逻辑即可。
